/**
 * @file lds_mic.c
 * @brief LDS Microphone Communication Protocol Stack Implementation
 * @details This file implements a complete communication protocol stack for microphone devices
 *          following the specified protocol format with big-endian byte order.
 *          This version includes a command queue for robust multi-command handling.
 * <AUTHOR> Team
 * @version 2.0
 * @date 2025-07-21
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * Protocol Format:
 * Head(1) + modelId(2) + CMD(2) + SEQ(1) + Addr(1) + LEN(2) + DATA(variable) + SUM(1)
 * - Big-endian byte order (MSB first)
 * - Head: Fixed 0x5A for all frames
 * - modelId: 0x0001-0xFFFFF device unique identifier
 * - SEQ: 0x00-0xFF sequence number for request/response matching
 * - Addr: Host=0x00, Slave=0x01/0x02/0x03, Slave broadcast=0xF1, Unused=0xEE, Global broadcast=0xFF
 * - CMD: Function code defining the operation
 * - LEN: Length of DATA field in bytes
 * - DATA: Variable length payload data
 * - SUM: Checksum (sum of all bytes from Head to SUM-1, lower 8 bits)
 */

#include <rtdevice.h>
#include <stdlib.h>
#include "lds_utils.h"
#include "lds_uart.h"
#include "lds_mic.h"
#include "lds_led_config.h"

#define DBG_TAG "MIC"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define MIC_VERSION_MAX_LEN     64
/* ================================ Configuration ========================== */
#define MIC_SERIAL_NAME         "uart4"
#define MIC_POWER_CTRL_PIN      "PE.0"
#define MIC_CMD_QUEUE_SIZE      16                           /**< Command queue size, supporting up to 16 pending commands */
#define MIC_MAX_ERROR_COUNT     10                           /**< Maximum consecutive errors before assume device is not connected */

/* ================================ Timeout Configuration ================== */
#define MIC_RESPONSE_TIMEOUT    (RT_TICK_PER_SECOND * 2)    /**< 2 seconds response timeout */
#define MIC_HEARTBEAT_TIMEOUT   (RT_TICK_PER_SECOND * 300)   /**< 300 seconds heartbeat timeout */
#define MIC_PARSE_TIMEOUT       (RT_TICK_PER_SECOND * 1)   /**< 1s parse timeout for state machine */
#define MIC_RETRY_COUNT         2                           /**< Maximum retry attempts */

/* ================================ Global Variables ======================= */
static rt_base_t g_micPowerCtrl = -1;       /**< Microphone power control pin */
static rt_device_t g_micDev = RT_NULL;       /**< UART device handle */
static struct rt_timer g_heartbeatTimer;    /**< Heartbeat timeout timer */
static struct rt_timer g_retransmissionTimer; /**< Retransmission timer for command queue */
static struct rt_timer g_parseTimer;        /**< Parse timeout timer for state machine */
static struct rt_mutex g_micMutex;          /**< Thread safety mutex */
static uint8_t g_errorCount = 0 ;       /**< Error count for consecutive errors */
static bool g_enable = true;            /**< Enable flag for microphone communication */

/* ================================ Protocol State Machine ================ */
static lds_mic_frame_t g_rxFrame;           /**< Current receiving frame */
static uint8_t g_rxBuffer[MIC_MAX_FRAME_LEN]; /**< Frame receive buffer */
static uint16_t g_rxIndex = 0;              /**< Current receive index */
static uint8_t g_currentSeq = 0;            /**< Current sequence number */

/* ================================ Command Queue Management ================ */
/**
 * @brief Pending command queue entry structure
 * @details Contains all information for a command awaiting ACK, including retry management.
 */
typedef struct {
    bool active;                            /**< Defines if this queue slot is in use */
    uint8_t seq;                            /**< Unique sequence number for this command */
    uint16_t modelId;                       /**< Model ID for the command */
    uint16_t cmd;                           /**< Command code */
    uint8_t addr;                           /**< Target device address */
    uint16_t dataLen;                       /**< Length of the data payload */
    uint8_t data[MIC_MAX_DATA_LEN];         /**< Data payload */
    uint8_t retryCount;                     /**< Current retry count */
    rt_tick_t sent_timestamp;               /**< System tick when the command was last sent */
} lds_mic_cmd_queue_entry_t;

static lds_mic_cmd_queue_entry_t g_cmdQueue[MIC_CMD_QUEUE_SIZE]; /**< Command queue */
static void ldsMicStartRetransmissionTimer(void);
static int ldsMicSendFrame(uint16_t modelId, uint16_t cmd, uint8_t addr,
                          const uint8_t *data, uint16_t dataLen);
static void ldsMicResetStateMachine(void);

static int8_t mic_status_main_mute = -1;
static int8_t mic_status_sub_mute = -1;
static int8_t mic_status_sound_mode = -1;
static char mic_version[MIC_VERSION_MAX_LEN] = {0};

/**
 * @brief Microphone protocol state enumeration
 * @details Defines the states for the protocol frame parsing state machine
 */
typedef enum {
    MIC_STATE_IDLE = 0,        /**< Waiting for frame header */
    MIC_STATE_MODEL_ID_H,      /**< Receiving model ID high byte */
    MIC_STATE_MODEL_ID_L,      /**< Receiving model ID low byte */
    MIC_STATE_CMD_H,           /**< Receiving command high byte */
    MIC_STATE_CMD_L,           /**< Receiving command low byte */
    MIC_STATE_SEQ,             /**< Receiving sequence number */
    MIC_STATE_ADDR,            /**< Receiving address */
    MIC_STATE_LEN_H,           /**< Receiving length high byte */
    MIC_STATE_LEN_L,           /**< Receiving length low byte */
    MIC_STATE_DATA,            /**< Receiving data payload */
    MIC_STATE_CHECKSUM,        /**< Receiving checksum */
} LDS_MIC_STATE_E;

static LDS_MIC_STATE_E g_rxState = MIC_STATE_IDLE;

static void ldsMicHandleArrayParams(uint8_t paramType, uint8_t paramValue)
{
    switch (paramType) {
        case LDS_MIC_PARAM_MUTE_CONTROL:
            LOG_I("Mute set to %d", paramValue);
            if((mic_status_main_mute != paramValue) && (g_rxFrame.addr == LDS_MIC_ADDR_HOST)){
                mic_status_main_mute = paramValue;
                if(paramValue != LDS_MIC_MUTE_ON){
                    ldsLedOff(ldsLedGetConfigPin(LED_MAIN_MUTE));
                } else {
                    ldsLedOn(ldsLedGetConfigPin(LED_MAIN_MUTE));
                }
            } else if ((mic_status_sub_mute != paramValue) && (g_rxFrame.addr == LDS_MIC_ADDR_SLAVE_1)) {
                mic_status_sub_mute = paramValue;
                if(paramValue != LDS_MIC_MUTE_ON){
                    ldsLedOff(ldsLedGetConfigPin(LED_SUB_MUTE));
                } else {
                    ldsLedOn(ldsLedGetConfigPin(LED_SUB_MUTE));
                }
            }
            break;
        case LDS_MIC_PARAM_SOUND_MODE:
            LOG_I("Sound mode set to %d", paramValue);
            if(g_rxFrame.addr != LDS_MIC_ADDR_HOST){
                LOG_E("Invalid address for sound mode: %d", g_rxFrame.addr);
                break;
            }
            if(paramValue != mic_status_sound_mode){
                mic_status_sound_mode = paramValue;
                if(paramValue == LDS_MIC_SOUND_MODE_STANDARD){
                    ldsLedOn(ldsLedGetConfigPin(LED_STD));
                    ldsLedOff(ldsLedGetConfigPin(LED_BOY));
                    ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
                }
                else if(paramValue == LDS_MIC_SOUND_MODE_FEMALE){
                    ldsLedOn(ldsLedGetConfigPin(LED_GIRL));
                    ldsLedOff(ldsLedGetConfigPin(LED_BOY));
                    ldsLedOff(ldsLedGetConfigPin(LED_STD));
                }
                else if(paramValue == LDS_MIC_SOUND_MODE_MALE){
                    ldsLedOn(ldsLedGetConfigPin(LED_BOY));
                    ldsLedOff(ldsLedGetConfigPin(LED_STD));
                    ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
                }
            }
            break;
        default:
            LOG_E("Invalid parameter type: %d", paramType);
            break;
    }
}

/**
 * @brief Reset microphone device
 * @details Performs hardware reset of the microphone device via power control pin
 */
static void ldsMicReset(void)
{
    if (g_micPowerCtrl <= 0) {
        LOG_E("MIC power control pin not initialized");
        return;
    }

    LOG_I("Resetting feeless mic");
    rt_pin_write(g_micPowerCtrl, PIN_LOW);
    rt_thread_mdelay(500);
    rt_pin_write(g_micPowerCtrl, PIN_HIGH);
    rt_thread_mdelay(100);
}

/**
 * @brief Heartbeat timeout handler
 * @details Called when heartbeat timeout occurs, triggers device reset
 *
 * @param parameter Timer parameter (unused)
 */
static void ldsMicHeartbeatTimeout(void *parameter)
{
    LOG_W("heartbeat timeout");
    // 重启会导致mic音效恢复默认
    // ldsMicReset();
    ldsMicSendFrame(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_VERSION_QUERY, LDS_MIC_ADDR_HOST, RT_NULL, 0);
}

/**
 * @brief Parse timeout handler
 * @details Called when parse timeout occurs, resets the state machine to prevent hanging
 *
 * @param parameter Timer parameter (unused)
 */
static void ldsMicParseTimeout(void *parameter)
{
    LOG_W("Parse timeout state %d, reset", g_rxState);
    ldsMicResetStateMachine();
}

/**
 * @brief Initialize the command queue
 * @details Clears all entries in the command queue, marking them as inactive.
 */
static void ldsMicInitCmdQueue(void)
{
    rt_memset(&g_cmdQueue, 0, sizeof(g_cmdQueue));
}

/**
 * @brief Find the oldest pending command in the queue.
 * @details Iterates through the queue to find the active command with the earliest
 *          sent timestamp. This command is considered the "head" for retransmission.
 * @return Pointer to the oldest command entry, or RT_NULL if the queue is empty.
 */
static lds_mic_cmd_queue_entry_t* ldsMicFindOldestCmd(void)
{
    lds_mic_cmd_queue_entry_t *oldest_cmd = RT_NULL;
    rt_tick_t min_timestamp = RT_TICK_MAX;

    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            if (oldest_cmd == RT_NULL || (g_cmdQueue[i].sent_timestamp < min_timestamp)) {
                min_timestamp = g_cmdQueue[i].sent_timestamp;
                oldest_cmd = &g_cmdQueue[i];
            }
        }
    }
    return oldest_cmd;
}

/**
 * @brief Retransmission timeout handler.
 * @details This function is called when the retransmission timer expires. It handles
 *          the retransmission or dropping of the oldest command in the queue.
 * @param parameter Unused.
 */
static void ldsMicRetransmissionTimeout(void *parameter)
{
    uint8_t seq_bk = 0;
    rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);

    lds_mic_cmd_queue_entry_t *cmd_to_retry = ldsMicFindOldestCmd();

    if (cmd_to_retry != RT_NULL) {
        if (cmd_to_retry->retryCount >= MIC_RETRY_COUNT) {
            LOG_E("Max retries for cmd=0x%04X, seq=%d. Dropping.", cmd_to_retry->cmd, cmd_to_retry->seq);
            cmd_to_retry->active = false; // Drop the command
            g_errorCount ++;
            if(g_errorCount > MIC_MAX_ERROR_COUNT){
                LOG_W("mic reach max error count,most likely not connected");
                g_enable = false;
                // rt_timer_stop(&g_heartbeatTimer);
            }
        } else {
            cmd_to_retry->retryCount++;
            LOG_W("Retrying cmd 0x%04X, seq=%d, attempt %d/%d",
                  cmd_to_retry->cmd, cmd_to_retry->seq, cmd_to_retry->retryCount, MIC_RETRY_COUNT);

            // Resend the command with the same sequence number
            seq_bk = g_currentSeq;
            g_currentSeq = cmd_to_retry->seq - 1; // ldsMicSendFrame will increment it back
            ldsMicSendFrame(cmd_to_retry->modelId, cmd_to_retry->cmd, cmd_to_retry->addr,
                              cmd_to_retry->data, cmd_to_retry->dataLen);
            cmd_to_retry->sent_timestamp = rt_tick_get();
            g_currentSeq = seq_bk;
        }
    }

    // After handling, always try to restart the timer for the next pending command
    ldsMicStartRetransmissionTimer();

    rt_mutex_release(&g_micMutex);
}

/**
 * @brief Starts the retransmission timer if there are pending commands.
 * @details Finds the oldest command and sets a one-shot timer for it.
 *          This function must be called with the mutex held.
 */
static void ldsMicStartRetransmissionTimer(void)
{
    rt_timer_stop(&g_retransmissionTimer);

    lds_mic_cmd_queue_entry_t *next_cmd = ldsMicFindOldestCmd();
    if (next_cmd != RT_NULL) {
        rt_tick_t timeout_tick = MIC_RESPONSE_TIMEOUT;
        // Optional: Can calculate remaining time if needed, but a fixed timeout is simpler.
        rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_SET_TIME, &timeout_tick);
        rt_timer_start(&g_retransmissionTimer);
    }
}

/**
 * @brief Get next sequence number
 * @details Generates the next sequence number for outgoing frames
 *
 * @return uint8_t Next sequence number (0x00-0xFF)
 */
static uint8_t ldsMicGetNextSeq(void)
{
    return ++g_currentSeq;
}

/**
 * @brief Send protocol frame
 * @details Constructs and sends a complete protocol frame with checksum.
 *          Note: This is the low-level send function and does not manage the queue.
 *
 * @param modelId Device model ID (big-endian)
 * @param cmd Command code (big-endian)
 * @param addr Target address
 * @param data Pointer to data payload (can be NULL if dataLen is 0)
 * @param dataLen Length of data payload
 * @return int 0 on success, negative error code on failure
 */
static int ldsMicSendFrame(uint16_t modelId, uint16_t cmd, uint8_t addr,
                          const uint8_t *data, uint16_t dataLen)
{
    uint8_t frame[MIC_MAX_FRAME_LEN];
    uint16_t frameLen;
    uint8_t checksum;
    rt_size_t written;
    uint8_t seq = 0;
    
    // if(!g_enable){
    //     LOG_D("MIC not enabled");
    //     return -RT_ERROR;
    // }

    seq = ldsMicGetNextSeq();

    if (g_micDev == RT_NULL) {
        LOG_E("MIC device not initialized");
        return -RT_ERROR;
    }

    if (dataLen > MIC_MAX_DATA_LEN) {
        LOG_E("Data length %d exceeds maximum %d", dataLen, MIC_MAX_DATA_LEN);
        return -RT_EINVAL;
    }

    /* Construct frame */
    frameLen = 0;
    frame[frameLen++] = MIC_FRAME_HEAD;                   /* Head */
    frame[frameLen++] = (uint8_t)(modelId >> 8);         /* Model ID high byte */
    frame[frameLen++] = (uint8_t)(modelId & 0xFF);       /* Model ID low byte */
    frame[frameLen++] = (uint8_t)(cmd >> 8);             /* CMD high byte */
    frame[frameLen++] = (uint8_t)(cmd & 0xFF);           /* CMD low byte */
    frame[frameLen++] = seq;                              /* SEQ */
    frame[frameLen++] = addr;                             /* Addr */
    frame[frameLen++] = (uint8_t)(dataLen >> 8);         /* LEN high byte */
    frame[frameLen++] = (uint8_t)(dataLen & 0xFF);       /* LEN low byte */

    /* Copy data payload */
    if (data != RT_NULL && dataLen > 0) {
        rt_memcpy(&frame[frameLen], data, dataLen);
        frameLen += dataLen;
    }

    /* Calculate and append checksum */
    checksum = ldsUtilCheckSum(frame, frameLen);
    frame[frameLen++] = checksum;

    /* Send frame */
    written = rt_device_write(g_micDev, 0, frame, frameLen);
    if (written != frameLen) {
        LOG_E("Failed to send complete frame, sent %d of %d bytes", written, frameLen);
        return -RT_ERROR;
    }

    LOG_D("Sent frame: modelId=0x%04X, cmd=0x%04X, seq=%d, addr=0x%02X, len=%d",
          modelId, cmd, seq, addr, dataLen);
    // LOG_HEX("mic-tx", 16, frame, frameLen);

    return seq; // Return the sequence number used
}

/**
 * @brief Sends a command and adds it to the pending queue.
 * @details This is the new main function for sending commands. It finds a free
 *          slot in the queue, sends the frame, and manages the retransmission timer.
 * @param modelId Device model ID
 * @param cmd Command code
 * @param addr Target address
 * @param data Pointer to data payload
 * @param dataLen Length of data payload
 * @return 0 on success, negative error code on failure.
 */
static int ldsMicSendCommand(uint16_t modelId, uint16_t cmd, uint8_t addr,
                               const uint8_t *data, uint16_t dataLen)
{
    if(!g_enable){
        LOG_D("MIC not enabled");
        return -RT_ERROR;
    }

    rt_err_t result = rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_E("Failed to acquire mutex: %d", result);
        return -RT_ERROR;
    }

    int free_slot_idx = -1;
    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (!g_cmdQueue[i].active) {
            free_slot_idx = i;
            break;
        }
    }

    if (free_slot_idx == -1) {
        LOG_E("Command queue is full. Cannot send cmd 0x%04X.", cmd);
        rt_mutex_release(&g_micMutex);
        return -RT_EBUSY;
    }

    // Backup current seq, as ldsMicSendFrame will modify it.
    uint8_t seq_bak = g_currentSeq;
    int seq_sent = ldsMicSendFrame(modelId, cmd, addr, data, dataLen);

    if (seq_sent < 0) {
        g_currentSeq = seq_bak; // Restore seq on failure
        rt_mutex_release(&g_micMutex);
        return seq_sent; // Propagate error
    }

    // Populate queue entry
    lds_mic_cmd_queue_entry_t *entry = &g_cmdQueue[free_slot_idx];
    entry->active = true;
    entry->seq = (uint8_t)seq_sent;
    entry->modelId = modelId;
    entry->cmd = cmd;
    entry->addr = addr;
    entry->dataLen = dataLen;
    if (dataLen > 0) {
        rt_memcpy(entry->data, data, dataLen);
    }
    entry->retryCount = 0;
    entry->sent_timestamp = rt_tick_get();

    LOG_D("Cmd 0x%04X with seq=%d added to queue.", cmd, entry->seq);

    // If the timer is not running (i.e., queue was empty), start it.
    rt_uint8_t timer_state = 0;
    rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_GET_STATE, &timer_state);
    if (timer_state == RT_TIMER_FLAG_DEACTIVATED) {
        ldsMicStartRetransmissionTimer();
    }

    rt_mutex_release(&g_micMutex);
    return 0;
}


/**
 * @brief Process received protocol frame
 * @details Handles complete received frames and dispatches based on command type
 *
 * @param frame Pointer to received frame structure
 * @return int 0 on success, negative error code on failure
 */
static int ldsMicProcessFrame(const lds_mic_frame_t *frame)
{
    if (frame == RT_NULL) {
        LOG_E("Invalid frame pointer");
        return -RT_EINVAL;
    }

    LOG_D("Processing frame: modelId=0x%04X, cmd=0x%04X, seq=%d, addr=0x%02X, len=%d",
          frame->modelId, frame->cmd, frame->seq, frame->addr, frame->dataLen);

    /* Reset heartbeat timer on any valid frame */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_start(&g_heartbeatTimer);

    g_errorCount = 0;
    g_enable = true;
    // This is an ACK or a response frame. Try to match it with a pending command.
    rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
    bool ack_matched = false;
    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active && g_cmdQueue[i].seq == frame->seq) {
            LOG_D("ACK received for seq=%d. Removing from queue.", frame->seq);
            g_cmdQueue[i].active = false; // Deactivate the command
            ack_matched = true;
            
            // The command was acknowledged. We need to check if we should restart the timer
            // for the next oldest command.
            ldsMicStartRetransmissionTimer();
            break;
        }
    }
    if (!ack_matched) {
         LOG_W("Received ACK for unexpected seq=%d or command type 0x%04X", frame->seq, frame->cmd);
    }
    rt_mutex_release(&g_micMutex);


    switch (frame->cmd) {
        case LDS_MIC_CMD_FACTORY_RESET:
            LOG_I("Factory reset command received");
            break;

        case LDS_MIC_CMD_REBOOT:
            LOG_I("Reboot command response received");
            break;

        case LDS_MIC_CMD_VERSION_QUERY:
            if (frame->dataLen > 0) {
                int max_len = frame->dataLen > MIC_VERSION_MAX_LEN - 1 ? MIC_VERSION_MAX_LEN - 1 : frame->dataLen;
                LOG_I("Version info received: %.*s", frame->dataLen, frame->data);
                rt_memcpy(mic_version, frame->data, max_len);
                mic_version[max_len] = '\0';
            }
            break;

        case LDS_MIC_CMD_SET_ARRAY_PARAMS:
            LOG_I("Array parameters received");
            if (frame->dataLen >= 2) {
                uint8_t paramType = frame->data[0];
                uint8_t paramValue = frame->data[1];
                LOG_I("Parameter type=0x%02X, value=0x%02X", paramType, paramValue);
                ldsMicHandleArrayParams(paramType, paramValue);
            }
            break;

        default:
            LOG_W("Unknown command received: 0x%04X", frame->cmd);
            return -RT_ERROR;
    }

    return 0;
}

/**
 * @brief Start parse timeout timer
 * @details Starts or restarts the parse timeout timer to prevent state machine hanging
 */
static void ldsMicStartParseTimer(void)
{
    rt_timer_stop(&g_parseTimer);
    rt_timer_start(&g_parseTimer);
}

/**
 * @brief Reset frame parsing state machine
 * @details Resets the state machine to idle state and clears buffers
 */
static void ldsMicResetStateMachine(void)
{
    /* Stop parse timeout timer */
    rt_timer_stop(&g_parseTimer);

    g_rxState = MIC_STATE_IDLE;
    g_rxIndex = 0;
    rt_memset(&g_rxFrame, 0, sizeof(g_rxFrame));
    rt_memset(g_rxBuffer, 0, sizeof(g_rxBuffer));
}

/**
 * @brief Protocol frame parsing state machine
 * @details Parses incoming bytes according to the protocol specification
 *
 * @param data Pointer to received data buffer
 * @param size Size of received data
 * @return int 0 on success, negative error code on failure
 */
static int ldsMicParseData(const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    for (rt_size_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        // LOG_D("state %d", g_rxState);
        switch (g_rxState) {
            case MIC_STATE_IDLE:
                if (byte == MIC_FRAME_HEAD) {
                    ldsMicResetStateMachine();
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head = byte;
                    g_rxState = MIC_STATE_MODEL_ID_H;
                    /* Start parse timeout timer when entering parsing state */
                    ldsMicStartParseTimer();
                }
                break;

            case MIC_STATE_MODEL_ID_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.modelId = (uint16_t)(byte << 8);
                g_rxState = MIC_STATE_MODEL_ID_L;
                /* Restart parse timeout timer on each valid byte */
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_MODEL_ID_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.modelId |= byte;
                g_rxState = MIC_STATE_CMD_H;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_CMD_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd = (uint16_t)(byte << 8);
                g_rxState = MIC_STATE_CMD_L;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_CMD_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd |= byte;
                g_rxState = MIC_STATE_SEQ;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_SEQ:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.seq = byte;
                g_rxState = MIC_STATE_ADDR;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_ADDR:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.addr = byte;
                g_rxState = MIC_STATE_LEN_H;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_LEN_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen = (uint16_t)(byte << 8);
                g_rxState = MIC_STATE_LEN_L;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_LEN_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen |= byte;

                /* Validate data length */
                if (g_rxFrame.dataLen > MIC_MAX_DATA_LEN) {
                    LOG_E("Invalid data length: %d", g_rxFrame.dataLen);
                    ldsMicResetStateMachine();
                    break;
                }

                if (g_rxFrame.dataLen == 0) {
                    g_rxState = MIC_STATE_CHECKSUM;
                } else {
                    g_rxState = MIC_STATE_DATA;
                }
                rt_memset(g_rxFrame.data, 0, sizeof(g_rxFrame.data));
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_DATA:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.data[g_rxIndex - 10] = byte;  /* Data starts at index 9 + 1 for checksum */

                if (g_rxIndex >= (9 + g_rxFrame.dataLen)) {
                    g_rxState = MIC_STATE_CHECKSUM;
                }
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_CHECKSUM:
                g_rxFrame.checksum = byte;
                g_rxBuffer[g_rxIndex++] = byte; // Also add checksum to buffer for verification

                /* Validate checksum */
                uint8_t calculatedChecksum = ldsUtilCheckSum(g_rxBuffer, g_rxIndex - 1);
                if (calculatedChecksum != g_rxFrame.checksum) {
                    LOG_E("Checksum mismatch: calculated=0x%02X, received=0x%02X",
                          calculatedChecksum, g_rxFrame.checksum);
                    LOG_HEX("mic-rx-err", 16, g_rxBuffer, g_rxIndex);
                    ldsMicResetStateMachine();
                    break;
                }

                // LOG_HEX("mic-rx", 16, g_rxBuffer, g_rxIndex);
                ldsMicProcessFrame(&g_rxFrame);
                ldsMicResetStateMachine();
                break;

            default:
                LOG_E("Invalid state: %d", g_rxState);
                ldsMicResetStateMachine();
                break;
        }

        /* Prevent buffer overflow */
        if (g_rxIndex >= MIC_MAX_FRAME_LEN) {
            LOG_E("Frame buffer overflow");
            ldsMicResetStateMachine();
            break;
        }
    }

    return 0;
}

/**
 * @brief UART data processing callback
 * @details Callback function registered with UART driver for data processing
 *
 * @param dev RT-Thread device handle
 * @param data Pointer to received data buffer
 * @param size Size of received data in bytes
 * @return int 0 on success, negative error code on failure
 */
int ldsMicProcess(rt_device_t dev, const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);

    if(ldsAtGetFactoryTestMode()){
        rt_device_write(g_micDev, 0, data, size);
        return 0;
    }
    // No mutex here, parse happens in UART's context.
    // Mutex is used inside process/ack logic.
    int ret = ldsMicParseData(data, size);

    return ret;
}

/* ================================ Public API Functions =================== */
uint8_t ldsMicGetStatus(void)
{
    uint8_t status = 0;
    status |= mic_status_main_mute == 1 ? 0x01 : 0x00;
    status |= mic_status_sub_mute == 1 ? 0x02 : 0x00;
    status |= (mic_status_sound_mode == -1 ? 0 : mic_status_sound_mode) >> 2;
    return status;
}
int ldsMicGetPowerCtrl(void)
{
    return rt_pin_read(g_micPowerCtrl);
}
int ldsMicSetPowerCtrl(int on)
{
    if (g_micPowerCtrl <= 0) {
        LOG_E("MIC power control pin not initialized");
        return -RT_ERROR;
    }

    rt_pin_write(g_micPowerCtrl, on ? PIN_HIGH : PIN_LOW);
    g_enable = on;
    if(!on){
        g_errorCount = MIC_MAX_ERROR_COUNT;
        rt_timer_stop(&g_heartbeatTimer);
    } else {
        g_errorCount = 0;
        rt_timer_stop(&g_heartbeatTimer);
        rt_timer_start(&g_heartbeatTimer);
    }
    return 0;
}

const char *ldsMicGetVersion(void)
{
    return mic_version;
}

int ldsMicQueryVersion(void)
{
    return ldsMicSendCommand(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_VERSION_QUERY, LDS_MIC_ADDR_HOST, RT_NULL, 0);
}

int ldsMicQueryStatus(void)
{
    ldsMicSetMuteControl(LDS_MIC_ADDR_HOST, LDS_MIC_MUTE_QUERY);
    rt_thread_mdelay(20);
    ldsMicSetMuteControl(LDS_MIC_ADDR_SLAVE_1, LDS_MIC_MUTE_QUERY);
    rt_thread_mdelay(20);
    ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_QUERY);
    return 0;
}

int ldsMicSetMuteByAddr(uint8_t addr)
{
    LDS_MIC_MUTE_E mute = LDS_MIC_MUTE_ON;
    if(LDS_MIC_ADDR_HOST == addr){
        mute =  mic_status_main_mute ? LDS_MIC_MUTE_OFF : LDS_MIC_MUTE_ON;
    } else {
        mute =  mic_status_sub_mute ? LDS_MIC_MUTE_OFF : LDS_MIC_MUTE_ON;
    }
    LOG_D("addr %02x mute %d", addr, mute);
    return ldsMicSetMuteControl(addr, mute);
}

int ldsMicSetMuteControl(uint8_t addr, LDS_MIC_MUTE_E muteState)
{
    int ret = 0;
    uint8_t data[2];

    if (muteState >= LDS_MIC_MUTE_MAX) {
        LOG_E("Invalid mute state: %d", muteState);
        return -RT_EINVAL;
    }

    data[0] = LDS_MIC_PARAM_MUTE_CONTROL;
    data[1] = (uint8_t)muteState;
    
    ret = ldsMicSendCommand(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_SET_ARRAY_PARAMS, addr, data, sizeof(data));

    if(!ret && (muteState < LDS_MIC_MUTE_QUERY)){
        if(LDS_MIC_ADDR_HOST == addr) {
            mic_status_main_mute = muteState;
        } else {
            mic_status_sub_mute = muteState;
        }
    }
    return ret;
}

int ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_E soundMode)
{
    int ret = 0;
    uint8_t data[2];

    if (soundMode >= LDS_MIC_SOUND_MODE_MAX) {
        LOG_E("Invalid sound mode: %d", soundMode);
        return -RT_EINVAL;
    }

    data[0] = LDS_MIC_PARAM_SOUND_MODE;
    data[1] = (uint8_t)soundMode;

    ret = ldsMicSendCommand(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_SET_ARRAY_PARAMS, LDS_MIC_ADDR_HOST, data, sizeof(data));
    if(!ret && (soundMode < LDS_MIC_SOUND_MODE_QUERY)){
        mic_status_sound_mode = soundMode;
    }
    return ret;
}

/**
 * @brief Initialize microphone communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete microphone system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 */
int ldsMicInit(void)
{
    rt_err_t result;

    /* Initialize mutex for thread safety */
    result = rt_mutex_init(&g_micMutex, "mic_mutex", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK) {
        LOG_E("Failed to initialize mutex: %d", result);
        return -RT_ERROR;
    }

    /* Initialize power control pin */
    g_micPowerCtrl = power_ctrl_pin_init(MIC_POWER_CTRL_PIN, PIN_HIGH);
    if (g_micPowerCtrl < 0) {
        LOG_E("Failed to initialize MIC power control pin %s", MIC_POWER_CTRL_PIN);
        rt_mutex_detach(&g_micMutex);
        return -RT_ERROR;
    }

    /* Initialize UART with callback */
    g_micDev = ldsUartInit(MIC_SERIAL_NAME, LDS_UART_INDEX_4, ldsMicProcess);
    if (g_micDev == RT_NULL) {
        LOG_E("Failed to initialize MIC UART %s", MIC_SERIAL_NAME);
        rt_mutex_detach(&g_micMutex);
        return -RT_ERROR;
    }

    /* Initialize heartbeat timer */
    rt_timer_init(&g_heartbeatTimer, "mic_hb",
                  ldsMicHeartbeatTimeout,
                  RT_NULL,
                  MIC_HEARTBEAT_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
    rt_timer_start(&g_heartbeatTimer);

    /* Initialize retransmission timer */
    rt_timer_init(&g_retransmissionTimer, "mic_retry",
                  ldsMicRetransmissionTimeout,
                  RT_NULL,
                  MIC_RESPONSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize parse timeout timer */
    rt_timer_init(&g_parseTimer, "mic_parse",
                  ldsMicParseTimeout,
                  RT_NULL,
                  MIC_PARSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize state machine */
    ldsMicResetStateMachine();
    g_currentSeq = 0;

    /* Initialize command queue */
    ldsMicInitCmdQueue();


    LOG_I("Microphone communication system initialized successfully");
    return 0;
}

/**
 * @brief Deinitialize microphone communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 */
int ldsMicDeinit(void)
{
    /* Stop timers */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_stop(&g_retransmissionTimer);
    rt_timer_stop(&g_parseTimer);
    rt_timer_detach(&g_heartbeatTimer);
    rt_timer_detach(&g_retransmissionTimer);
    rt_timer_detach(&g_parseTimer);

    /* Reset state machine */
    ldsMicResetStateMachine();
    
    /* Clear command queue */
    rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
    ldsMicInitCmdQueue();
    rt_mutex_release(&g_micMutex);

    /* Close UART device */
    if (g_micDev != RT_NULL) {
        rt_device_close(g_micDev);
        g_micDev = RT_NULL;
    }

    /* Power down device */
    if (g_micPowerCtrl > 0) {
        rt_pin_write(g_micPowerCtrl, PIN_LOW);
        g_micPowerCtrl = -1;
    }

    /* Cleanup mutex */
    rt_mutex_detach(&g_micMutex);

    LOG_I("Microphone communication system deinitialized");
    return 0;
}

/* ================================ MSH Debug Commands ===================== */

static int ldsMicQueueStatus(void)
{
    rt_kprintf("Command Queue Status (Size: %d):\n", MIC_CMD_QUEUE_SIZE);
    bool empty = true;
    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            empty = false;
            rt_kprintf("  Slot %d: [ACTIVE]\n", i);
            rt_kprintf("    seq: %d, cmd: 0x%04X, addr: 0x%02X\n",
                       g_cmdQueue[i].seq, g_cmdQueue[i].cmd, g_cmdQueue[i].addr);
            rt_kprintf("    retries: %d, sent_at: %u\n",
                       g_cmdQueue[i].retryCount, g_cmdQueue[i].sent_timestamp);
        }
    }
    if (empty) {
        rt_kprintf("  Queue is empty.\n");
    }
    return 0;
}


/**
 * @brief MSH command for microphone operations
 * @details Provides command-line interface for testing microphone communication
 *
 * @param argc Argument count
 * @param argv Argument vector
 * @return int 0 on success, negative error code on failure
 */
static int ldsMicCmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: mic <command> [args...]\n");
        rt_kprintf("Commands:\n");
        rt_kprintf("  init                    - Initialize microphone system\n");
        rt_kprintf("  deinit                  - Deinitialize microphone system\n");
        rt_kprintf("  reset                   - Reset microphone device\n");
        rt_kprintf("  version                 - Query version information\n");
        rt_kprintf("  mute <addr> <state>     - Set mute control (0=unmute, 1=mute, 255=query)\n");
        rt_kprintf("  sound <mode>            - Set sound mode (0=standard, 1=female, 2=male, 255=query)\n");
        rt_kprintf("  status                  - Show system status and queue\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "init") == 0) {
        int ret = ldsMicInit();
        rt_kprintf("Microphone init %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "deinit") == 0) {
        int ret = ldsMicDeinit();
        rt_kprintf("Microphone deinit %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "reset") == 0) {
        ldsMicReset();
        rt_kprintf("Microphone device reset\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "version") == 0) {
        int ret = ldsMicQueryVersion();
        rt_kprintf("Version query %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "mute") == 0) {
        if (argc != 4) {
            rt_kprintf("Usage: mic mute <addr> <state>\n");
            rt_kprintf("  state: 0=unmute, 1=mute, 255=query\n");
            return -RT_EINVAL;
        }
        uint8_t addr = (uint8_t)strtoul(argv[2], RT_NULL, 0);
        uint8_t state = (uint8_t)strtoul(argv[3], RT_NULL, 0);
        int ret = ldsMicSetMuteControl(addr, (LDS_MIC_MUTE_E)state);
        rt_kprintf("Mute control command %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "sound") == 0) {
        if (argc != 3) {
            rt_kprintf("Usage: mic sound <mode>\n");
            rt_kprintf("  mode: 0=standard, 1=female, 2=male, 255=query\n");
            return -RT_EINVAL;
        }
        uint8_t mode = (uint8_t)strtoul(argv[2], RT_NULL, 0);
        int ret = ldsMicSetSoundMode((LDS_MIC_SOUND_MODE_E)mode);
        rt_kprintf("Sound mode command %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "status") == 0) {
        rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
        rt_kprintf("Microphone System Status:\n");
        rt_kprintf("  Device: %s\n", g_micDev ? "initialized" : "not initialized");
        rt_kprintf("  Power Control: %s\n", g_micPowerCtrl > 0 ? "enabled" : "disabled");
        rt_kprintf("  Current Sequence: %d\n", g_currentSeq);
        rt_kprintf("  RX State: %d\n", g_rxState);
        rt_kprintf("  RX Index: %d\n", g_rxIndex);

        /* Check parse timer state */
        rt_uint8_t parse_timer_state = 0;
        rt_timer_control(&g_parseTimer, RT_TIMER_CTRL_GET_STATE, &parse_timer_state);
        rt_kprintf("  Parse Timer: %s\n", parse_timer_state == RT_TIMER_FLAG_ACTIVATED ? "active" : "inactive");

        ldsMicQueueStatus();
        rt_mutex_release(&g_micMutex);
        return 0;
    }

    rt_kprintf("Unknown command: %s\n", argv[1]);
    return -RT_EINVAL;
}

MSH_CMD_EXPORT_ALIAS(ldsMicCmd, mic, Microphone communication protocol commands);
