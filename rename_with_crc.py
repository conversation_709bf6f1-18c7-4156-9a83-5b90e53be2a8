#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件重命名工具
将文件重命名为 AUX-EDUAudioProcessor-0001-V{version}-CRC32.bin 格式
"""

import os
import sys
import argparse
import zlib
from pathlib import Path


def calculate_crc32(file_path):
    """
    计算文件的CRC32值
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        str: CRC32值的十六进制字符串（大写）
    """
    crc32_value = 0
    try:
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(8192)  # 8KB chunks
                if not chunk:
                    break
                crc32_value = zlib.crc32(chunk, crc32_value)
        
        # 确保返回正数并转换为8位十六进制字符串
        crc32_value = crc32_value & 0xFFFFFFFF
        return f"{crc32_value:08X}"
    
    except IOError as e:
        print(f"错误：无法读取文件 {file_path}: {e}")
        return None


def rename_file(file_path, version):
    """
    重命名文件为指定格式
    
    Args:
        file_path (str): 原文件路径
        version (str): 版本号
        
    Returns:
        bool: 重命名是否成功
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return False
    
    # 计算CRC32
    print(f"正在计算文件 {file_path} 的CRC32值...")
    crc32_value = calculate_crc32(file_path)
    if crc32_value is None:
        return False
    
    print(f"CRC32值: {crc32_value}")
    
    # 获取文件所在目录
    file_dir = os.path.dirname(file_path)
    if not file_dir:
        file_dir = "."
    
    # 构造新文件名
    new_filename = f"AUX-EDUAudioProcessor-0001-V{version}-{crc32_value}.bin"
    new_file_path = os.path.join(file_dir, new_filename)
    
    # 检查新文件名是否已存在
    if os.path.exists(new_file_path):
        response = input(f"文件 {new_file_path} 已存在，是否覆盖？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return False
    
    # 重命名文件
    try:
        os.rename(file_path, new_file_path)
        print(f"文件重命名成功:")
        print(f"  原文件名: {os.path.basename(file_path)}")
        print(f"  新文件名: {new_filename}")
        print(f"  完整路径: {new_file_path}")
        return True
    
    except OSError as e:
        print(f"错误：重命名文件失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="将文件重命名为 AUX-EDUAudioProcessor-0001-V{version}-CRC32.bin 格式",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python rename_with_crc.py firmware.bin
  python rename_with_crc.py /path/to/file.bin 
  python rename_with_crc.py -f firmware.bin 
        """
    )
    
    parser.add_argument(
        'filename',
        nargs='?',
        help='要重命名的文件路径'
    )
        
    parser.add_argument(
        '-f', '--file',
        dest='filename_alt',
        help='要重命名的文件路径 (替代位置参数)'
    )
        
    args = parser.parse_args()
    
    # 获取文件名
    filename = args.filename or args.filename_alt
    # board.h文件中提取版本号
    version = None
    try:
        with open('board/board.h', 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip().startswith('#define APP_VERSION'):
                    version = line.split('"')[1]
                    break
    except IOError as e:
        print(f"错误：无法读取main.c文件: {e}")
        sys.exit(1)
    # 检查参数
    if not filename:
        print("错误：请提供文件名")
        parser.print_help()
        sys.exit(1)
    
    if not version:
        print("错误：请提供版本号")
        parser.print_help()
        sys.exit(1)
    
    # 验证版本号格式（可选）
    if not version.replace('.', '').replace('-', '').replace('_', '').isalnum():
        print(f"警告：版本号 '{version}' 包含特殊字符，可能导致文件名问题")
    
    # 执行重命名
    success = rename_file(filename, version)
    
    if success:
        print("\n重命名操作完成！")
        sys.exit(0)
    else:
        print("\n重命名操作失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
