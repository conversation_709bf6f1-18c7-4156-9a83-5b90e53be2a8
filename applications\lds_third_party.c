/**
 * @file lds_third_party.c
 * @brief LDS third party Communication Protocol Stack Implementation
 * @details This file implements a complete communication protocol stack for third party devices
 *          following the specified protocol format with big-endian byte order.
 *          This version includes a command queue for robust multi-command handling.
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * Protocol Format:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(variable) + CRC16(2)
 * - Big-endian byte order (MSB first)
 * - Head: Fixed 0x5AA5 for all frames
 * - SEQ: 0x00-0xFF sequence number for request/response matching
 * - CMD: Function code defining the operation
 * - LEN: Length of DATA field in bytes
 * - DATA: Variable length payload data
 * - CRC16: 16-bit CRC checksum (CRC16 of all bytes from Head to CRC16-1)
 */

#include <rtdevice.h>
#include <stdlib.h>
#include <string.h>
#include "lds_utils.h"
#include "lds_uart.h"
#include "lds_third_party.h"
#include "lds_led_config.h"
#include "lds_uac.h"
#include "lds_mic.h"
#include "lds_key.h"
#include "lds_smart_base.h"

#define DBG_TAG "THIRD_PARTY"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

/* ================================ Configuration ========================== */
#define THIRD_PARTY_SERIAL_NAME         "uart7"
#define THIRD_PARTY_POWER_CTRL_USB      "PD.0"
#define THIRD_PARTY_CMD_QUEUE_SIZE      16                           /**< Command queue size, supporting up to 16 pending commands */
#define THIRD_PARTY_MAX_ERROR_COUNT     10                           /**< Maximum consecutive errors before asxore device is not connected */
#define THIRD_PARTY_RESPONSE_FLAG       0x80
/* ================================ Timeout Configuration ================== */
#define THIRD_PARTY_RESPONSE_TIMEOUT    (RT_TICK_PER_SECOND * 2)    /**< 2 seconds response timeout */
#define THIRD_PARTY_PARSE_TIMEOUT       (RT_TICK_PER_SECOND * 1)   /**< 1s parse timeout for state machine */
#define THIRD_PARTY_RETRY_COUNT         2                           /**< Maximum retry attempts */

/* ================================ Global Variables ======================= */
static rt_device_t g_thirdPartyDev = RT_NULL;       /**< UART device handle */
static rt_base_t g_powerCtrlUsb = -1;            /* usb电源控制引脚 */
static struct rt_timer g_retransmissionTimer; /**< Retransmission timer for command queue */
static struct rt_timer g_parseTimer;        /**< Parse timeout timer for state machine */
static struct rt_mutex g_thirdPartyMutex;          /**< Thread safety mutex */
static uint8_t g_errorCount = 0 ;       /**< Error count for consecutive errors */

/* ================================ Protocol State Machine ================ */
static lds_third_party_frame_t g_rxFrame;           /**< Current receiving frame */
static uint8_t g_rxBuffer[THIRD_PARTY_MAX_FRAME_LEN]; /**< Frame receive buffer */
static uint16_t g_rxIndex = 0;              /**< Current receive index */
static uint8_t g_currentSeq = 0;            /**< Current sequence number */

/* ================================ Command Queue Management ================ */
/**
 * @brief Pending command queue entry structure
 * @details Contains all information for a command awaiting ACK, including retry management.
 */
typedef struct {
    bool active;                            /**< Defines if this queue slot is in use */
    uint8_t seq;                            /**< Unique sequence number for this command */
    uint8_t cmd;                           /**< Command code */
    uint16_t dataLen;                       /**< Length of the data payload */
    uint8_t data[THIRD_PARTY_MAX_DATA_LEN];         /**< Data payload */
    uint8_t retryCount;                     /**< Current retry count */
    rt_tick_t sent_timestamp;               /**< System tick when the command was last sent */
} lds_third_party_cmd_queue_entry_t;

static lds_third_party_cmd_queue_entry_t g_cmdQueue[THIRD_PARTY_CMD_QUEUE_SIZE]; /**< Command queue */
static void ldsThirdPartyStartRetransmissionTimer(void);
static int ldsThirdPartySendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen);
static void ldsThirdPartyResetStateMachine(void);

/**
 * @brief Third party protocol state enumeration
 * @details Defines the states for the protocol frame parsing state machine
 */
typedef enum {
    THIRD_PARTY_STATE_IDLE = 0,        /**< Waiting for frame header */
    THIRD_PARTY_STATE_HEAD_1,          /**< Receiving head 1 0x5a */
    THIRD_PARTY_STATE_HEAD_2,          /**< Receiving head 2 0xa5 */
    THIRD_PARTY_STATE_CMD,           /**< Receiving command high byte */
    THIRD_PARTY_STATE_SEQ,             /**< Receiving sequence number */
    THIRD_PARTY_STATE_LEN_H,           /**< Receiving length high byte */
    THIRD_PARTY_STATE_LEN_L,           /**< Receiving length low byte */
    THIRD_PARTY_STATE_DATA,            /**< Receiving data payload */
    THIRD_PARTY_STATE_CRC16_H,         /**< Receiving CRC16 high byte */
    THIRD_PARTY_STATE_CRC16_L,         /**< Receiving CRC16 low byte */
} LDS_THIRD_PARTY_STATE_E;

static LDS_THIRD_PARTY_STATE_E g_rxState = THIRD_PARTY_STATE_IDLE;

/**
 * @brief Parse timeout handler
 * @details Called when parse timeout occurs, resets the state machine to prevent hanging
 *
 * @param parameter Timer parameter (unused)
 */
static void ldsThirdPartyParseTimeout(void *parameter)
{
    LOG_W("Parse timeout state %d, reset", g_rxState);
    ldsThirdPartyResetStateMachine();
}

/**
 * @brief Initialize the command queue
 * @details Clears all entries in the command queue, marking them as inactive.
 */
static void ldsThirdPartyInitCmdQueue(void)
{
    rt_memset(&g_cmdQueue, 0, sizeof(g_cmdQueue));
}

/**
 * @brief Find the oldest pending command in the queue.
 * @details Iterates through the queue to find the active command with the earliest
 *          sent timestamp. This command is considered the "head" for retransmission.
 * @return Pointer to the oldest command entry, or RT_NULL if the queue is empty.
 */
static lds_third_party_cmd_queue_entry_t* ldsThirdPartyFindOldestCmd(void)
{
    lds_third_party_cmd_queue_entry_t *oldest_cmd = RT_NULL;
    rt_tick_t min_timestamp = RT_TICK_MAX;

    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            if (oldest_cmd == RT_NULL || (g_cmdQueue[i].sent_timestamp < min_timestamp)) {
                min_timestamp = g_cmdQueue[i].sent_timestamp;
                oldest_cmd = &g_cmdQueue[i];
            }
        }
    }
    return oldest_cmd;
}

/**
 * @brief Retransmission timeout handler.
 * @details This function is called when the retransmission timer expires. It handles
 *          the retransmission or dropping of the oldest command in the queue.
 * @param parameter Unused.
 */
static void ldsThirdPartyRetransmissionTimeout(void *parameter)
{
    uint8_t seq_bk = 0;
    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);

    lds_third_party_cmd_queue_entry_t *cmd_to_retry = ldsThirdPartyFindOldestCmd();

    if (cmd_to_retry != RT_NULL) {
        if (cmd_to_retry->retryCount >= THIRD_PARTY_RETRY_COUNT) {
            LOG_E("Max retries for cmd=0x%02X, seq=%d. Dropping.", cmd_to_retry->cmd, cmd_to_retry->seq);
            cmd_to_retry->active = false; // Drop the command
            g_errorCount ++;
            if(g_errorCount > THIRD_PARTY_MAX_ERROR_COUNT){
                LOG_D("THIRD_PARTY reach max error count,most likely not connected");
            }
        } else {
            cmd_to_retry->retryCount++;
            LOG_W("Retrying cmd 0x%04X, seq=%d, attempt %d/%d",
                  cmd_to_retry->cmd, cmd_to_retry->seq, cmd_to_retry->retryCount, THIRD_PARTY_RETRY_COUNT);

            // Resend the command with the same sequence number
            seq_bk = g_currentSeq;
            g_currentSeq = cmd_to_retry->seq - 1; // ldsThirdPartySendFrame will increment it back
            ldsThirdPartySendFrame(cmd_to_retry->cmd, cmd_to_retry->data, cmd_to_retry->dataLen);
            cmd_to_retry->sent_timestamp = rt_tick_get();
            g_currentSeq = seq_bk;
        }
    }

    // After handling, always try to restart the timer for the next pending command
    ldsThirdPartyStartRetransmissionTimer();

    rt_mutex_release(&g_thirdPartyMutex);
}

/**
 * @brief Starts the retransmission timer if there are pending commands.
 * @details Finds the oldest command and sets a one-shot timer for it.
 *          This function must be called with the mutex held.
 */
static void ldsThirdPartyStartRetransmissionTimer(void)
{
    rt_timer_stop(&g_retransmissionTimer);

    lds_third_party_cmd_queue_entry_t *next_cmd = ldsThirdPartyFindOldestCmd();
    if (next_cmd != RT_NULL) {
        rt_tick_t timeout_tick = THIRD_PARTY_RESPONSE_TIMEOUT;
        // Optional: Can calculate remaining time if needed, but a fixed timeout is simpler.
        rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_SET_TIME, &timeout_tick);
        rt_timer_start(&g_retransmissionTimer);
    }
}

/**
 * @brief Get next sequence number
 * @details Generates the next sequence number for outgoing frames
 *
 * @return uint8_t Next sequence number (0x00-0xFF)
 */
static uint8_t ldsThirdPartyGetNextSeq(void)
{
    return ++g_currentSeq;
}

/**
 * @brief Send protocol frame
 * @details Constructs and sends a complete protocol frame with CRC16.
 *          Note: This is the low-level send function and does not manage the queue.
 *
 * @param cmd Command code (big-endian)
 * @param data Pointer to data payload (can be NULL if dataLen is 0)
 * @param dataLen Length of data payload
 * @return int 0 on success, negative error code on failure
 */
static int ldsThirdPartySendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen)
{
    uint8_t frame[THIRD_PARTY_MAX_FRAME_LEN];
    uint16_t frameLen;
    uint16_t crc16;
    rt_size_t written;
    uint8_t seq = 0;

    seq = ldsThirdPartyGetNextSeq();

    if (g_thirdPartyDev == RT_NULL) {
        LOG_E("THIRD_PARTY device not initialized");
        return -RT_ERROR;
    }

    if (dataLen > THIRD_PARTY_MAX_DATA_LEN) {
        LOG_E("Data length %d exceeds maximum %d", dataLen, THIRD_PARTY_MAX_DATA_LEN);
        return -RT_EINVAL;
    }

    /* Construct frame */
    frameLen = 0;
    frame[frameLen++] = THIRD_PARTY_FRAME_HEAD1;          /* Head1 */
    frame[frameLen++] = THIRD_PARTY_FRAME_HEAD2;          /* Head2 */
    frame[frameLen++] = cmd;                             /* CMD */
    frame[frameLen++] = seq;                             /* SEQ */
    frame[frameLen++] = (uint8_t)(dataLen >> 8);         /* LEN high byte */
    frame[frameLen++] = (uint8_t)(dataLen & 0xFF);       /* LEN low byte */

    /* Copy data payload */
    if (data != RT_NULL && dataLen > 0) {
        rt_memcpy(&frame[frameLen], data, dataLen);
        frameLen += dataLen;
    }

    /* Calculate and append CRC16 */
    crc16 = ldsUtilCheckCrc16(frame, frameLen);
    frame[frameLen++] = (uint8_t)(crc16 >> 8);       /* CRC16 high byte */
    frame[frameLen++] = (uint8_t)(crc16 & 0xFF);     /* CRC16 low byte */

    /* Send frame */
    written = rt_device_write(g_thirdPartyDev, 0, frame, frameLen);
    if (written != frameLen) {
        LOG_E("Failed to send complete frame, sent %d of %d bytes", written, frameLen);
        return -RT_ERROR;
    }

    LOG_D("Sent frame:  cmd=0x%02X, seq=%d, len=%d", cmd, seq, dataLen);
    // LOG_HEX("third_party-tx", 16, frame, frameLen);

    return seq; // Return the sequence number used
}

/**
 * @brief Sends a command and adds it to the pending queue.
 * @details This is the new main function for sending commands. It finds a free
 *          slot in the queue, sends the frame, and manages the retransmission timer.
 * @param cmd Command code
 * @param data Pointer to data payload
 * @param dataLen Length of data payload
 * @return 0 on success, negative error code on failure.
 */
int ldsThirdPartySendCommand(uint16_t cmd, const uint8_t *data, uint16_t dataLen)
{
    rt_err_t result = rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_E("Failed to acquire mutex: %d", result);
        return -RT_ERROR;
    }

    int free_slot_idx = -1;
    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (!g_cmdQueue[i].active) {
            free_slot_idx = i;
            break;
        }
    }

    if (free_slot_idx == -1) {
        LOG_E("Command queue is full. Cannot send cmd 0x%04X.", cmd);
        rt_mutex_release(&g_thirdPartyMutex);
        return -RT_EBUSY;
    }

    // Backup current seq, as ldsThirdPartySendFrame will modify it.
    uint8_t seq_bak = g_currentSeq;
    int seq_sent = ldsThirdPartySendFrame(cmd, data, dataLen);

    if (seq_sent < 0) {
        g_currentSeq = seq_bak; // Restore seq on failure
        rt_mutex_release(&g_thirdPartyMutex);
        return seq_sent; // Propagate error
    }

    // Populate queue entry
    lds_third_party_cmd_queue_entry_t *entry = &g_cmdQueue[free_slot_idx];
    entry->active = true;
    entry->seq = (uint8_t)seq_sent;
    entry->cmd = cmd;
    entry->dataLen = dataLen;
    if (dataLen > 0) {
        rt_memcpy(entry->data, data, dataLen);
    }
    entry->retryCount = 0;
    entry->sent_timestamp = rt_tick_get();

    LOG_D("Cmd 0x%04X with seq=%d added to queue.", cmd, entry->seq);

    // If the timer is not running (i.e., queue was empty), start it.
    rt_uint8_t timer_state = 0;
    rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_GET_STATE, &timer_state);
    if (timer_state == RT_TIMER_FLAG_DEACTIVATED) {
        ldsThirdPartyStartRetransmissionTimer();
    }

    rt_mutex_release(&g_thirdPartyMutex);
    return 0;
}
static int ldsThirdPartySendResponse(lds_third_party_frame_t *frame, int ack)
{
    int ret = 0;
    uint8_t seq_bk = 0;

    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    frame->dataLen += 1;
    if(frame->dataLen > THIRD_PARTY_MAX_DATA_LEN){
        LOG_E("command data len %d error", frame->dataLen);
        frame->dataLen -= 1;
    }
    frame->data[frame->dataLen - 1] = ack;
    seq_bk = g_currentSeq;
    g_currentSeq = frame->seq - 1; // ldsThirdPartySendFrame will increment it back
    ret = ldsThirdPartySendFrame(frame->cmd | THIRD_PARTY_RESPONSE_FLAG, frame->data, frame->dataLen);
    g_currentSeq = seq_bk;
    rt_mutex_release(&g_thirdPartyMutex);
    return ret;
}
static int ldsThirdPartySendMuteCmd(bool main, int mute)
{
    int ret = 0;
    ret = ldsMicSetMuteControl(main ? LDS_MIC_ADDR_HOST : LDS_MIC_ADDR_SLAVE_BROADCAST, mute);
    if(ret){
        LOG_E("Send mute cmd failed");
        return ret;
    }
    if(mute != LDS_MIC_MUTE_ON){
        ret = ldsLedOff(ldsLedGetConfigPin(LED_MAIN_MUTE));
    } else {
        ret = ldsLedOn(ldsLedGetConfigPin(LED_MAIN_MUTE));
    }
    return ret;
}
static int ldsThirdPartySendEffectCmd(int effect)
{
    int ret = 0;
    ret = ldsMicSetSoundMode(effect);
    if(ret){
        LOG_E("Send effect cmd failed");
        return ret;
    }
    if(effect == LDS_MIC_SOUND_MODE_STANDARD){
        ret = ldsLedOn(ldsLedGetConfigPin(LED_STD));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_BOY));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
    }
    else if(effect == LDS_MIC_SOUND_MODE_FEMALE){
        ret = ldsLedOn(ldsLedGetConfigPin(LED_GIRL));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_BOY));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_STD));
    }
    else if(effect == LDS_MIC_SOUND_MODE_MALE){
        ret = ldsLedOn(ldsLedGetConfigPin(LED_BOY));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_STD));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
    }
    return ret;
}
static int ldsThirdPartyVersionQuery(lds_third_party_frame_t *frame)
{
    int ret = 0;
    size_t str_len = 0;
    const char* version = RT_NULL;

    if(frame == RT_NULL){
        LOG_E("version frame null");
        return -1;
    }

    if(frame->data[0] == 0x00){
        version = ldsMicGetVersion();
    } else {
        version = ldsSmartBaseGetVersion();
    }
    str_len = strlen(version);
    if(str_len == 0) {
        LOG_E("version data len 0");
        return -1;
    }
    frame->dataLen = str_len + 1;
    if(frame->dataLen >= THIRD_PARTY_MAX_DATA_LEN){
        LOG_E("version data len %d error", str_len);
        frame->dataLen = 1;
        ret = -1;
    } else {
        rt_memcpy(&frame->data[1], version, str_len);
    }
    return ret;
}
static int ldsThirdPartyStatusQuery(lds_third_party_frame_t *frame)
{
    int ret = 0;
    if(frame == RT_NULL){
        LOG_E("status frame null");
        return -1;
    }
    frame->data[0] = ldsMicGetStatus();
    frame->data[1] = ldsSmartBaseGetStatus();
    ret = ldsMicGetPowerCtrl();
    if(ret < 0){
        LOG_E("read mic power ctrl pin failed");
        return ret;
    }
    frame->data[2] = ret;
    ret = rt_pin_read(g_powerCtrlUsb);
    if(ret < 0){
        LOG_E("read usb power ctrl pin failed");
        return ret;
    }
    frame->data[3] = ret;
    frame->dataLen = 4;
    ret = 0;
    return ret;
}
/**
 * @brief Process received protocol frame
 * @details Handles complete received frames and dispatches based on command type
 *
 * @param frame Pointer to received frame structure
 * @return int 0 on success, negative error code on failure
 */
static int ldsThirdPartyProcessFrame(lds_third_party_frame_t *frame)
{
    int ret = -1;
    if (frame == RT_NULL) {
        LOG_E("Invalid frame pointer");
        return -RT_EINVAL;
    }

    LOG_D("Processing frame: cmd=0x%02X, seq=%d, len=%d",
          frame->cmd, frame->seq, frame->dataLen);

    g_errorCount = 0;
    // This is an ACK or a response frame. Try to match it with a pending command.
    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    bool ack_matched = false;
    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active && g_cmdQueue[i].seq == frame->seq) {
            LOG_D("ACK received for seq=%d. Removing from queue.", frame->seq);
            g_cmdQueue[i].active = false; // Deactivate the command
            ack_matched = true;
            
            // The command was acknowledged. We need to check if we should restart the timer
            // for the next oldest command.
            ldsThirdPartyStartRetransmissionTimer();
            break;
        }
    }
    if (!ack_matched) {
        LOG_D("Received ACK for unexpected seq=%d or command type 0x%02X", frame->seq, frame->cmd);
    }
    rt_mutex_release(&g_thirdPartyMutex);

    switch (frame->cmd) {
        case LDS_THIRD_PARTY_CMD_MUTE:
            if(frame->dataLen != 2){
                LOG_E("MUTE command data len %d error", frame->dataLen);
            } else {
                LOG_I("MUTE command: device=%d, mute=%d", frame->data[0], frame->data[1]);
                ret = ldsThirdPartySendMuteCmd(frame->data[0] == 0x00, frame->data[1]);
            }
            break; 
        case LDS_THIRD_PARTY_CMD_EFFECT:
            LOG_I("effect: %d", frame->data[0]);
            ret = ldsThirdPartySendEffectCmd(frame->data[0]);
            break;
        case LDS_THIRD_PARTY_CMD_POWER_CTRL:
            if(frame->dataLen != 2){
                LOG_E("power ctrl command data len %d error", frame->dataLen);
            } else {
                LOG_I("power ctrl command: device=%d, ctrl=%d", frame->data[0], frame->data[1]);
                if(frame->data[0] == 0x00){
                    ret = ldsMicSetPowerCtrl(frame->data[1]);
                } else {
                    rt_pin_write(g_powerCtrlUsb, frame->data[1] ? PIN_HIGH : PIN_LOW);
                    ret = 0;
                }
            }
            break;           
        case LDS_THIRD_PARTY_CMD_VERSION:
            if (frame->dataLen != 1) {
                LOG_E("version command data len %d error", frame->dataLen);
            } else {
                LOG_I("version command: device=%d", frame->data[0]);
                ret = ldsThirdPartyVersionQuery(frame);
            }
            break;

        case LDS_THIRD_PARTY_CMD_STATUS:
            LOG_I("status command");
            ret = ldsThirdPartyStatusQuery(frame);
            break;
        default:
            LOG_W("Unknown command received: 0x%04X", frame->cmd);
            return -RT_ERROR;
    }
    ldsThirdPartySendResponse(frame, ret);
    return 0;
}

/**
 * @brief Start parse timeout timer
 * @details Starts or restarts the parse timeout timer to prevent state machine hanging
 */
static void ldsThirdPartyStartParseTimer(void)
{
    rt_timer_stop(&g_parseTimer);
    rt_timer_start(&g_parseTimer);
}

/**
 * @brief Reset frame parsing state machine
 * @details Resets the state machine to idle state and clears buffers
 */
static void ldsThirdPartyResetStateMachine(void)
{
    /* Stop parse timeout timer */
    rt_timer_stop(&g_parseTimer);

    g_rxState = THIRD_PARTY_STATE_IDLE;
    g_rxIndex = 0;
    rt_memset(&g_rxFrame, 0, sizeof(g_rxFrame));
    rt_memset(g_rxBuffer, 0, sizeof(g_rxBuffer));
}

/**
 * @brief Protocol frame parsing state machine
 * @details Parses incoming bytes according to the protocol specification
 *
 * @param data Pointer to received data buffer
 * @param size Size of received data
 * @return int 0 on success, negative error code on failure
 */
static int ldsThirdPartyParseData(const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    for (rt_size_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        // LOG_D("state %d", g_rxState);
        switch (g_rxState) {
            case THIRD_PARTY_STATE_IDLE:
                if (byte == THIRD_PARTY_FRAME_HEAD1) {
                    ldsThirdPartyResetStateMachine();
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head1 = byte;
                    g_rxState = THIRD_PARTY_STATE_HEAD_1;
                    /* Start parse timeout timer when entering parsing state */
                    ldsThirdPartyStartParseTimer();
                }
                break;

            case THIRD_PARTY_STATE_HEAD_1:
                if (byte == THIRD_PARTY_FRAME_HEAD2) {
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head2 = byte;
                    g_rxState = THIRD_PARTY_STATE_CMD;
                    /* Start parse timeout timer when entering parsing state */
                    ldsThirdPartyStartParseTimer();
                }
                break;

            case THIRD_PARTY_STATE_CMD:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd = byte;
                g_rxState = THIRD_PARTY_STATE_SEQ;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_SEQ:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.seq = byte;
                g_rxState = THIRD_PARTY_STATE_LEN_H;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_LEN_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen = (uint16_t)(byte << 8);
                g_rxState = THIRD_PARTY_STATE_LEN_L;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_LEN_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen |= byte;

                /* Validate data length */
                if (g_rxFrame.dataLen > THIRD_PARTY_MAX_DATA_LEN) {
                    LOG_E("Invalid data length: %d", g_rxFrame.dataLen);
                    ldsThirdPartyResetStateMachine();
                    break;
                }

                if (g_rxFrame.dataLen == 0) {
                    g_rxState = THIRD_PARTY_STATE_CRC16_H;
                } else {
                    g_rxState = THIRD_PARTY_STATE_DATA;
                }
                rt_memset(g_rxFrame.data, 0, sizeof(g_rxFrame.data));
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_DATA:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.data[g_rxIndex - 7] = byte;  /* Data starts at index 6 */

                if (g_rxIndex >= (6 + g_rxFrame.dataLen)) {
                    g_rxState = THIRD_PARTY_STATE_CRC16_H;
                }
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_CRC16_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.crc16 = (uint16_t)(byte << 8);
                g_rxState = THIRD_PARTY_STATE_CRC16_L;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_CRC16_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.crc16 |= byte;

                /* Validate CRC16 */
                uint16_t calculatedCrc16 = ldsUtilCheckCrc16(g_rxBuffer, g_rxIndex - 2);
                if (calculatedCrc16 != g_rxFrame.crc16) {
                    LOG_E("CRC16 mismatch: calculated=0x%04X, received=0x%04X",
                          calculatedCrc16, g_rxFrame.crc16);
                    LOG_HEX("third_party-rx-err", 16, g_rxBuffer, g_rxIndex);
                    ldsThirdPartyResetStateMachine();
                    break;
                }

                // LOG_HEX("third_party-rx", 16, g_rxBuffer, g_rxIndex);
                ldsThirdPartyProcessFrame(&g_rxFrame);
                ldsThirdPartyResetStateMachine();
                break;

            default:
                LOG_E("Invalid state: %d", g_rxState);
                ldsThirdPartyResetStateMachine();
                break;
        }

        /* Prevent buffer overflow */
        if (g_rxIndex >= THIRD_PARTY_MAX_FRAME_LEN) {
            LOG_E("Frame buffer overflow");
            ldsThirdPartyResetStateMachine();
            break;
        }
    }

    return 0;
}

/**
 * @brief UART data processing callback
 * @details Callback function registered with UART driver for data processing
 *
 * @param dev RT-Thread device handle
 * @param data Pointer to received data buffer
 * @param size Size of received data in bytes
 * @return int 0 on success, negative error code on failure
 */
int ldsThirdPartyProcess(rt_device_t dev, const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);
    
    if(ldsAtGetFactoryTestMode()){
        rt_device_write(g_thirdPartyDev, 0, data, size);
        return 0;
    }
    // No mutex here, parse happens in UART's context.
    // Mutex is used inside process/ack logic.
    int ret = ldsThirdPartyParseData(data, size);

    return ret;
}

/* ================================ Public API Functions =================== */

/**
 * @brief Initialize third party communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete third party system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 */
int ldsThirdPartyInit(void)
{
    rt_err_t result;

    /* Initialize mutex for thread safety */
    result = rt_mutex_init(&g_thirdPartyMutex, "third_party_mutex", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK) {
        LOG_E("Failed to initialize mutex: %d", result);
        return -RT_ERROR;
    }
    
    /* Initialize power control pin */
    g_powerCtrlUsb = power_ctrl_pin_init(THIRD_PARTY_POWER_CTRL_USB, PIN_HIGH);
    if (g_powerCtrlUsb < 0) {
        LOG_E("Failed to initialize USB power control pin %s", THIRD_PARTY_POWER_CTRL_USB);
        rt_mutex_detach(&g_thirdPartyMutex);
        return -RT_ERROR;
    }

    /* Initialize UART with callback */
    g_thirdPartyDev = ldsUartInit(THIRD_PARTY_SERIAL_NAME, LDS_UART_INDEX_7, ldsThirdPartyProcess);
    if (g_thirdPartyDev == RT_NULL) {
        LOG_E("Failed to initialize THIRD_PARTY UART %s", THIRD_PARTY_SERIAL_NAME);
        rt_mutex_detach(&g_thirdPartyMutex);
        return -RT_ERROR;
    }

    /* Initialize retransmission timer */
    rt_timer_init(&g_retransmissionTimer, "thrd_retry",
                  ldsThirdPartyRetransmissionTimeout,
                  RT_NULL,
                  THIRD_PARTY_RESPONSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize parse timeout timer */
    rt_timer_init(&g_parseTimer, "thrd_parse",
                  ldsThirdPartyParseTimeout,
                  RT_NULL,
                  THIRD_PARTY_PARSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize state machine */
    ldsThirdPartyResetStateMachine();
    g_currentSeq = 0;

    /* Initialize command queue */
    ldsThirdPartyInitCmdQueue();


    LOG_I("Third party communication system initialized successfully");
    return 0;
}

/**
 * @brief Deinitialize third party communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 */
int ldsThirdPartyDeinit(void)
{
    /* Stop timers */
    rt_timer_stop(&g_retransmissionTimer);
    rt_timer_stop(&g_parseTimer);
    rt_timer_detach(&g_retransmissionTimer);
    rt_timer_detach(&g_parseTimer);

    /* Reset state machine */
    ldsThirdPartyResetStateMachine();
    
    /* Clear command queue */
    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    ldsThirdPartyInitCmdQueue();
    rt_mutex_release(&g_thirdPartyMutex);

    /* Close UART device */
    if (g_thirdPartyDev != RT_NULL) {
        rt_device_close(g_thirdPartyDev);
        g_thirdPartyDev = RT_NULL;
    }

    /* Cleanup mutex */
    rt_mutex_detach(&g_thirdPartyMutex);

    LOG_I("Third party communication system deinitialized");
    return 0;
}

/* ================================ MSH Debug Commands ===================== */

static int ldsThirdPartyQueueStatus(void)
{
    rt_kprintf("Command Queue Status (Size: %d):\n", THIRD_PARTY_CMD_QUEUE_SIZE);
    bool empty = true;
    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            empty = false;
            rt_kprintf("  Slot %d: [ACTIVE]\n", i);
            rt_kprintf("    seq: %d, cmd: 0x%04X,\n",
                       g_cmdQueue[i].seq, g_cmdQueue[i].cmd);
            rt_kprintf("    retries: %d, sent_at: %u\n",
                       g_cmdQueue[i].retryCount, g_cmdQueue[i].sent_timestamp);
        }
    }
    if (empty) {
        rt_kprintf("  Queue is empty.\n");
    }
    return 0;
}


/**
 * @brief MSH command for third party operations
 * @details Provides command-line interface for testing third party communication
 *
 * @param argc Argument count
 * @param argv Argument vector
 * @return int 0 on success, negative error code on failure
 */
static int ldsThirdPartyCmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: third_party <command> [args...]\n");
        rt_kprintf("Commands:\n");
        rt_kprintf("  init                    - Initialize third party system\n");
        rt_kprintf("  deinit                  - Deinitialize third party system\n");
        rt_kprintf("  reset                   - Reset third party device\n");
        rt_kprintf("  version                 - Query version information\n");
        rt_kprintf("  status                  - Show system status and queue\n");
        rt_kprintf("  test_crc16              - Test CRC16 implementation\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "init") == 0) {
        int ret = ldsThirdPartyInit();
        rt_kprintf("Third party init %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "deinit") == 0) {
        int ret = ldsThirdPartyDeinit();
        rt_kprintf("Third party deinit %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "status") == 0) {
        rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
        rt_kprintf("Third party System Status:\n");
        rt_kprintf("  Device: %s\n", g_thirdPartyDev ? "initialized" : "not initialized");
        rt_kprintf("  Current Sequence: %d\n", g_currentSeq);
        rt_kprintf("  RX State: %d\n", g_rxState);
        rt_kprintf("  RX Index: %d\n", g_rxIndex);

        /* Check parse timer state */
        rt_uint8_t parse_timer_state = 0;
        rt_timer_control(&g_parseTimer, RT_TIMER_CTRL_GET_STATE, &parse_timer_state);
        rt_kprintf("  Parse Timer: %s\n", parse_timer_state == RT_TIMER_FLAG_ACTIVATED ? "active" : "inactive");

        ldsThirdPartyQueueStatus();
        rt_mutex_release(&g_thirdPartyMutex);
        return 0;
    }

    rt_kprintf("Unknown command: %s\n", argv[1]);
    return -RT_EINVAL;
}

MSH_CMD_EXPORT_ALIAS(ldsThirdPartyCmd, third_party, Third party communication protocol commands);
