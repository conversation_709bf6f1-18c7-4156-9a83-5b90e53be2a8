#include "lds_at.h"
#include "board.h"
#include <rtthread.h>
#include <at.h>

#define DBG_TAG "ATcmd"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define AT_REBOOT_TIMEOUT    (RT_TICK_PER_SECOND / 2)    /**< 500ms*/

static bool g_factoryTestMode = false;
static struct rt_timer g_atRebootTimer; 

static void ldsAtRebootTimeout(void *parameter)
{
    rt_kprintf("reboot now!");
    rt_hw_cpu_reset();
}

bool ldsAtGetFactoryTestMode(void)
{
    return g_factoryTestMode;
}

static at_result_t atFactoryTestStart(void)
{
    LOG_W("go into factory test mode!");
    g_factoryTestMode = true;
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+TSTART", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atFactoryTestStart);

static at_result_t atFactoryTestStop(void)
{
    LOG_W("exit from factory test mode!");
    g_factoryTestMode = false;
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+TSTOP", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atFactoryTestStop);

static at_result_t atGetFwInfo(void)
{
    LOG_I("get firmware info");
    at_server_printfln("+TFWINFO:");
    at_server_printfln("Mode ID:%s", MODEL_ID);
    at_server_printfln("FW ID:%s", APP_VERSION);
    at_server_printfln("Date Code:%s", DATECODE);
    at_server_printfln("HW ID:%s", HW_VERSION);
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+TFWINFO", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atGetFwInfo);

static at_result_t atReboot(void)
{
    LOG_W("reboot system!");
    rt_timer_init(&g_atRebootTimer, "atReboot",
                ldsAtRebootTimeout,
                RT_NULL,
                AT_REBOOT_TIMEOUT,
                RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);
    return AT_RESULT_OK;
}
AT_CMD_EXPORT("AT+REBOOT", RT_NULL, RT_NULL, RT_NULL, RT_NULL, atReboot);